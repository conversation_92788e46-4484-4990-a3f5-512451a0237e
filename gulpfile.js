import { exec } from "node:child_process";
import gulp from "gulp"

gulp.task("data", (done) => {
    exec("tsc", (error) => {
        if (error) {
            console.error(error);
            return done(error);
        }
        exec("node dist/main.js", (error) => {
            if (error) {
                console.error(error);
                return done(error);
            }
            done();
        });
    });
});
const Ccommand = "gcc.exe src/main/main.c -Isrc/main -o dist/main -lglfw3 -lopengl32 -lgdi32";
gulp.task("compile", (done) => {
    exec(Ccommand, (error) => {
        if (error) {
            console.error(error);
            return done(error);
        }
        done();
    });
});
