// Combined TypeScript file - generated by tsBind.py

import * as fs from 'node:fs';


// ===== core.ts =====

namespace core {
    export abstract class gameComponet<RecordShaping extends {name:string}>{
        public static empty:gameComponet<any>;
        public abstract ToJSON():RecordShaping;
        public name:string;
    }
    class X extends gameComponet<{name:string}>{
        public ToJSON():{name:string}{
            return {name:this.name};
        }
    }
    export class RegistryFunc{
        public static all: (()=>Promise<void>)[] = [];
        constructor(public func:()=>Promise<void>){
            RegistryFunc.all.push(func);
        }
        static async run(){
            let promised:Promise<void>[] = []
            RegistryFunc.all.forEach((value)=>{
                promised.push(value());
            })
            await Promise.all(promised);
            let out:string = JSON.stringify(registries.registry.all)
            fs.writeFileSync("out.json", out);
        }
    }
    gameComponet.empty = new X();
    type NonFunctionKeys<T> = {
        [K in keyof T]: T[K] extends Function ? never : K;
    }[keyof T];
    export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>
}

// ===== registries.ts =====

namespace registries {
    export abstract class registry<T extends core.gameComponet<U>, U extends {name:string}>{
        static all: Record<string, registry<any, any>[]> = {};
        protected registry: Map<[string, string], T> = new Map();
        constructor(protected type:string, protected space:string){
            registry.all[this.type].push(this);
        }
        public get(what:string):T{
            return this.registry.get([this.space, what]) as T;
        }
        public add(value:T):T{
            this.registry.set([this.space, value.name], value);
            return value;
        }
        public toArray():U[]{
            let a:U[] = [];
            Array.from(this.registry.values()).forEach((value, index)=>{
                a[index] = value.ToJSON();
                a[index].name = this.space + ":" + a[index].name;
            })
            return a
        }
    }
}

// ===== block.ts =====

namespace block {
    type point = [number, number, number]&{length:3};
    type box = ([point, point ]&{length:2})
    export class blockProperties{
        public flammable:boolean = false;
        Flamable(a:boolean){
            this.flammable = a;
            return this;
        }
        hitbox: box[] = [[[0,0,0],[16,16,16]]]
        Hitbox(a:box[]){
            this.hitbox = a;
            return this;
        }
    }
    export type blockJSON = {
        properties:core.NonFunctionProperties<blockProperties>;
        name:string
    };
    export class block extends core.gameComponet<blockJSON>{
        public ToJSON():blockJSON{
            return {
                properties:this.properties,
                name:this.name
            };
        }
        constructor(name:string, public properties:blockProperties){
            super();
            this.name = name;
        }
    }
    export class blockRegistry extends registries.registry<block, blockJSON>{
        constructor(space:string){
            super("block", space);
        }
    }
    export let airBlock;
    export let bedrockBlock;
    export let f = new core.RegistryFunc(async ()=>{
        let blockReg: blockRegistry = new blockRegistry("_");
        const airBlock = blockReg.add(new block("_", new blockProperties().Hitbox([[[0,0,0],[0,0,0]]])));  
        const bedrockBlock = blockReg.add(new block("bedrock", new blockProperties()));
        console.log(blockReg);
    });
}

// ===== item.ts =====

namespace item {
    export class ItemProerties{
        public flammable:boolean = true;
        Flamable(a:boolean){
            this.flammable = a;
            return this;
        }
    }
    export type ItemBehaviour = {name:string}
    export type ItemJSON = {
        properties: core.NonFunctionProperties<ItemProerties>;
        name: string;
        behaviour: ItemBehaviour;
        attachedComponent: core.gameComponet<any>;
    }
    export type behaviourProcesor<T extends ItemBehaviour> = 
        T extends {name:"block_item"} ? block.block : 
        core.gameComponet<any>;
    export class Item<V extends ItemBehaviour> extends core.gameComponet<ItemJSON>{
        static readonly Item:ItemBehaviour = {name:"item"};
        static readonly BlockItem:ItemBehaviour = {name:"block_item"};
        public properties: ItemProerties;
        public attachedComponent:behaviourProcesor<ItemBehaviour>;
        constructor(name:string, public readonly behaviour:V, properties:ItemProerties, attachedComponent?:behaviourProcesor<V>){
            super();
            this.name = name;
            this.properties = properties;
            this.attachedComponent = attachedComponent as core.gameComponet<any>;
            if(attachedComponent == undefined){
                if(behaviour.name == "block_item"){
                    throw new Error("block_item requires a block to be attached");
                }
                this.attachedComponent = core.gameComponet.empty;
            }
        }
        public ToJSON() {
            return {
                name: this.name,
                properties: this.properties,
                behaviour: this.behaviour,
                attachedComponent: this.attachedComponent.ToJSON()
            }
        }
    }
    export class ItemRegistry extends registries.registry<Item<any>, ItemJSON>{
        constructor(space:string){
            super("item", space);
        }
    }
    export let bedrock;
    export let air;
    export let f = new core.RegistryFunc(async ()=>{
        let itemRegistry: ItemRegistry = new ItemRegistry("_");
        bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem ,new ItemProerties()));  
        air = itemRegistry.add(new Item("_", Item.BlockItem, new ItemProerties()));
    });
}

// ===== main.ts =====

core.RegistryFunc.run()
