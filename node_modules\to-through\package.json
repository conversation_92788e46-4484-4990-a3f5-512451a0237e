{"name": "to-through", "version": "3.0.0", "description": "Wrap a Readable stream in a Transform stream.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/to-through", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"streamx": "^2.12.5"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.4.2", "mocha": "^8.4.0", "nyc": "^15.1.0", "readable-stream": "^3.6.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["transform", "readable", "through", "wrap"]}