{"name": "resolve-dir", "description": "Resolve a directory that is either local, global or in the user's home directory.", "version": "1.0.1", "homepage": "https://github.com/jonschlinkert/resolve-dir", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/resolve-dir", "bugs": {"url": "https://github.com/jonschlinkert/resolve-dir/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "devDependencies": {"gulp-format-md": "^1.0.0", "homedir-polyfill": "^1.0.1", "mocha": "^3.5.0"}, "keywords": ["dir", "directory", "expansion", "file", "filepath", "fp", "global", "home", "modules", "npm", "path", "resolve", "tilde", "user", "user-home", "userhome"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["expand-tilde", "findup-sync", "resolve-modules"]}, "reflinks": ["verb", "verb-generate-readme"], "lint": {"reflinks": true}}}