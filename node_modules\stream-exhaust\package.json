{"name": "stream-exhaust", "version": "1.0.2", "description": "Ensure that a stream is flowing data without mutating it", "main": "index.js", "scripts": {"test": "node test.js"}, "repository": {"type": "git", "url": "https://github.com/chris<PERSON><PERSON>on/stream-exhaust.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/chris<PERSON><PERSON>on/stream-exhaust.git"}, "homepage": "https://github.com/chris<PERSON><PERSON>on/stream-exhaust.git", "devDependencies": {"readable-stream": "^1.0.31", "tape": "^2.14.0", "through2": "^0.5.1"}}