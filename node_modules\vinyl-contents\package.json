{"name": "vinyl-contents", "version": "2.0.0", "description": "Utility to read the contents of a vinyl file.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/vinyl-contents", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"bl": "^5.0.0", "vinyl": "^3.0.0"}, "devDependencies": {"eslint": "^7.0.0", "eslint-config-gulp": "^5.0.0", "eslint-plugin-node": "^11.1.0", "expect": "^27.0.0", "mocha": "^8.0.0", "nyc": "^15.0.0", "readable-stream": "^3.6.0", "streamx": "^2.12.5"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": []}