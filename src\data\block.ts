import * as core from "./core";
import * as registries from "./registries";

type point = [number, number, number]&{length:3};
type box = ([point, point ]&{length:2})
export class blockProperties{
    public flammable:boolean = false;
    Flamable(a:boolean){
        this.flammable = a;
        return this;
    }
    hitbox: box[] = [[[0,0,0],[16,16,16]]]
    Hitbox(a:box[]){
        this.hitbox = a;
        return this;
    }
}
export type blockJSON = {
    properties:core.NonFunctionProperties<blockProperties>;
    name:string
};
export class block extends core.gameComponet<blockJSON>{
    public ToJSON():blockJSON{
        return {
            properties:this.properties,
            name:this.name
        };
    }
    constructor(name:string, public properties:blockProperties){
        super();
        this.name = name;
    }
}
export class blockRegistry extends registries.registry<block, blockJSON>{
    constructor(space:string){
        super("block", space);
    }
}
export let airBlock;
export let bedrockBlock;
export let f = new core.RegistryFunc(async ()=>{
    let blockReg: blockRegistry = new blockRegistry("_");
    const airBlock = blockReg.add(new block("_", new blockProperties().Hitbox([[[0,0,0],[0,0,0]]])));  
    const bedrockBlock = blockReg.add(new block("bedrock", new blockProperties()));
    console.log(blockReg);
});