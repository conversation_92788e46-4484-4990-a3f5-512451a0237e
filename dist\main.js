"use strict";
// Combined TypeScript file - generated by tsBind.py
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var fs = require("node:fs");
// ===== core.ts =====
var core;
(function (core) {
    var gameComponet = /** @class */ (function () {
        function gameComponet() {
        }
        return gameComponet;
    }());
    core.gameComponet = gameComponet;
    var X = /** @class */ (function (_super) {
        __extends(X, _super);
        function X() {
            return _super !== null && _super.apply(this, arguments) || this;
        }
        X.prototype.ToJSON = function () {
            return { name: this.name };
        };
        return X;
    }(gameComponet));
    var RegistryFunc = /** @class */ (function () {
        function RegistryFunc(func) {
            this.func = func;
            RegistryFunc.all.push(func);
        }
        RegistryFunc.run = function () {
            return __awaiter(this, void 0, void 0, function () {
                var promised, out;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            promised = [];
                            RegistryFunc.all.forEach(function (value) {
                                promised.push(value());
                            });
                            return [4 /*yield*/, Promise.all(promised)];
                        case 1:
                            _a.sent();
                            out = JSON.stringify(registries.registry.all);
                            fs.writeFileSync("out.json", out);
                            return [2 /*return*/];
                    }
                });
            });
        };
        RegistryFunc.all = [];
        return RegistryFunc;
    }());
    core.RegistryFunc = RegistryFunc;
    gameComponet.empty = new X();
})(core || (core = {}));
// ===== registries.ts =====
var registries;
(function (registries) {
    var registry = /** @class */ (function () {
        function registry(type, space) {
            this.type = type;
            this.space = space;
            this.registry = new Map();
            registry.all[this.type].push(this);
        }
        registry.prototype.get = function (what) {
            return this.registry.get([this.space, what]);
        };
        registry.prototype.add = function (value) {
            this.registry.set([this.space, value.name], value);
            return value;
        };
        registry.prototype.toArray = function () {
            var _this = this;
            var a = [];
            Array.from(this.registry.values()).forEach(function (value, index) {
                a[index] = value.ToJSON();
                a[index].name = _this.space + ":" + a[index].name;
            });
            return a;
        };
        registry.all = {};
        return registry;
    }());
    registries.registry = registry;
})(registries || (registries = {}));
// ===== block.ts =====
var block;
(function (block_1) {
    var _this = this;
    var blockProperties = /** @class */ (function () {
        function blockProperties() {
            this.flammable = false;
            this.hitbox = [[[0, 0, 0], [16, 16, 16]]];
        }
        blockProperties.prototype.Flamable = function (a) {
            this.flammable = a;
            return this;
        };
        blockProperties.prototype.Hitbox = function (a) {
            this.hitbox = a;
            return this;
        };
        return blockProperties;
    }());
    block_1.blockProperties = blockProperties;
    var block = /** @class */ (function (_super) {
        __extends(block, _super);
        function block(name, properties) {
            var _this = _super.call(this) || this;
            _this.properties = properties;
            _this.name = name;
            return _this;
        }
        block.prototype.ToJSON = function () {
            return {
                properties: this.properties,
                name: this.name
            };
        };
        return block;
    }(core.gameComponet));
    block_1.block = block;
    var blockRegistry = /** @class */ (function (_super) {
        __extends(blockRegistry, _super);
        function blockRegistry(space) {
            return _super.call(this, "block", space) || this;
        }
        return blockRegistry;
    }(registries.registry));
    block_1.blockRegistry = blockRegistry;
    block_1.f = new core.RegistryFunc(function () { return __awaiter(_this, void 0, void 0, function () {
        var blockReg, airBlock, bedrockBlock;
        return __generator(this, function (_a) {
            blockReg = new blockRegistry("_");
            airBlock = blockReg.add(new block("_", new blockProperties().Hitbox([[[0, 0, 0], [0, 0, 0]]])));
            bedrockBlock = blockReg.add(new block("bedrock", new blockProperties()));
            console.log(blockReg);
            return [2 /*return*/];
        });
    }); });
})(block || (block = {}));
// ===== item.ts =====
var item;
(function (item) {
    var _this = this;
    var ItemProerties = /** @class */ (function () {
        function ItemProerties() {
            this.flammable = true;
        }
        ItemProerties.prototype.Flamable = function (a) {
            this.flammable = a;
            return this;
        };
        return ItemProerties;
    }());
    item.ItemProerties = ItemProerties;
    var Item = /** @class */ (function (_super) {
        __extends(Item, _super);
        function Item(name, behaviour, properties, attachedComponent) {
            var _this = _super.call(this) || this;
            _this.behaviour = behaviour;
            _this.name = name;
            _this.properties = properties;
            _this.attachedComponent = attachedComponent;
            if (attachedComponent == undefined) {
                if (behaviour.name == "block_item") {
                    throw new Error("block_item requires a block to be attached");
                }
                _this.attachedComponent = core.gameComponet.empty;
            }
            return _this;
        }
        Item.prototype.ToJSON = function () {
            return {
                name: this.name,
                properties: this.properties,
                behaviour: this.behaviour,
                attachedComponent: this.attachedComponent.ToJSON()
            };
        };
        Item.Item = { name: "item" };
        Item.BlockItem = { name: "block_item" };
        return Item;
    }(core.gameComponet));
    item.Item = Item;
    var ItemRegistry = /** @class */ (function (_super) {
        __extends(ItemRegistry, _super);
        function ItemRegistry(space) {
            return _super.call(this, "item", space) || this;
        }
        return ItemRegistry;
    }(registries.registry));
    item.ItemRegistry = ItemRegistry;
    item.f = new core.RegistryFunc(function () { return __awaiter(_this, void 0, void 0, function () {
        var itemRegistry;
        return __generator(this, function (_a) {
            itemRegistry = new ItemRegistry("_");
            item.bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem, new ItemProerties()));
            item.air = itemRegistry.add(new Item("_", Item.BlockItem, new ItemProerties()));
            return [2 /*return*/];
        });
    }); });
})(item || (item = {}));
// ===== main.ts =====
core.RegistryFunc.run();
