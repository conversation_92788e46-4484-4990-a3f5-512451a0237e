import { gameComponet } from "./main";

export class registry<T extends gameComponet<U>, U extends {name:string}>{
    protected registry: Map<[string, string], T> = new Map();
    constructor(protected space:string){}
    public get(what:string):T{
        return this.registry.get([this.space, what]);
    }
    public add(value:T):T{
        this.registry.set([this.space, value.name], value);
        return value;
    }
    public toArray():U[]{
        let a:U[] = [];
        Array.from(this.registry.values()).forEach((value, index)=>{
            a[index] = value.ToJSON();
        })
        return a
    }
}