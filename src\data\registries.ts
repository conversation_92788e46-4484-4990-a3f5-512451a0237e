import * as core from "./core";

export abstract class registry<T extends core.gameComponet<U>, U extends {name:string}>{
    static all: Record<string, registry<any, any>[]> = {};
    protected registry: Map<[string, string], T> = new Map();
    constructor(protected type:string, protected space:string){
        registry.all[this.type].push(this);
    }
    public get(what:string):T{
        return this.registry.get([this.space, what]) as T;
    }
    public add(value:T):T{
        this.registry.set([this.space, value.name], value);
        return value;
    }
    public toArray():U[]{
        let a:U[] = [];
        Array.from(this.registry.values()).forEach((value, index)=>{
            a[index] = value.ToJSON();
            a[index].name = this.space + ":" + a[index].name;
        })
        return a
    }
}