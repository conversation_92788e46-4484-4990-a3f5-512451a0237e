import * as registries from "./registries";
import * as fs from "node:fs";

export abstract class gameComponet<RecordShaping extends {name:string}>{
    public static empty:gameComponet<any>;
    public abstract ToJSON():RecordShaping;
    public name:string;
}
class X extends gameComponet<{name:string}>{
    public ToJSON():{name:string}{
        return {name:this.name};
    }
}
export class RegistryFunc{
    public static all: (()=>Promise<void>)[] = [];
    constructor(public func:()=>Promise<void>){
        RegistryFunc.all.push(func);
    }
    static async run(){
        let promised:Promise<void>[] = []
        RegistryFunc.all.forEach((value)=>{
            promised.push(value());
        })
        await Promise.all(promised);
        let out:string = JSON.stringify(registries.registry.all)
        fs.writeFileSync("out.json", out);
    }
}
gameComponet.empty = new X();
type NonFunctionKeys<T> = {
    [K in keyof T]: T[K] extends Function ? never : K;
}[keyof T];
export type NonFunctionProperties<T> = Pick<T, NonFunctionKeys<T>>