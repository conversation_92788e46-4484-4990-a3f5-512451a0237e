{"name": "vinyl-sourcemap", "version": "2.0.0", "description": "Add/write sourcemaps to/from Vinyl files.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON>", "<PERSON> <<EMAIL>>"], "repository": "gulpjs/vinyl-sourcemap", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js", "lib/"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"convert-source-map": "^2.0.0", "graceful-fs": "^4.2.10", "now-and-later": "^3.0.0", "streamx": "^2.12.5", "vinyl": "^3.0.0", "vinyl-contents": "^2.0.0"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.5.1", "mocha": "^8.4.0", "nyc": "^15.1.0", "readable-stream": "^3.6.0"}, "nyc": {"extension": [".js"], "reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["vinyl", "sourcemap", "gulp"]}