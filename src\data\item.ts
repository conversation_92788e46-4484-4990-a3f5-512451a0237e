import { gameComponet } from "./main";
import { registry } from "./registries";
export class ItemProerties{}
export class Item extends gameComponet<{
    properties: ItemProerties
    name: string
}>{
    public properties: ItemProerties;
    public ToJSON() {
        return {
            name: this.name,
            properties: this.properties
        }
    }
}
let registr: registry<Item> = new registry<Item>();