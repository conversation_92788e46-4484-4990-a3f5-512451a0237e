import * as block from "./block";
import * as core from "./core";
import * as registries from "./registries";
export class ItemProerties{
    public flammable:boolean = true;
    Flamable(a:boolean){
        this.flammable = a;
        return this;
    }
}
export type ItemBehaviour = {name:string}
export type ItemJSON = {
    properties: core.NonFunctionProperties<ItemProerties>;
    name: string;
    behaviour: ItemBehaviour;
    attachedComponent: core.gameComponet<any>;
}
export type behaviourProcesor<T extends ItemBehaviour> = 
    T extends {name:"block_item"} ? block.block : 
    core.gameComponet<any>;
export class Item<V extends ItemBehaviour> extends core.gameComponet<ItemJSON>{
    static readonly Item:ItemBehaviour = {name:"item"};
    static readonly BlockItem:ItemBehaviour = {name:"block_item"};
    public properties: ItemProerties;
    public attachedComponent:behaviourProcesor<ItemBehaviour>;
    constructor(name:string, public readonly behaviour:V, properties:ItemProerties, attachedComponent?:behaviourProcesor<V>){
        super();
        this.name = name;
        this.properties = properties;
        this.attachedComponent = attachedComponent as core.gameComponet<any>;
        if(attachedComponent == undefined){
            if(behaviour.name == "block_item"){
                throw new Error("block_item requires a block to be attached");
            }
            this.attachedComponent = core.gameComponet.empty;
        }
    }
    public ToJSON() {
        return {
            name: this.name,
            properties: this.properties,
            behaviour: this.behaviour,
            attachedComponent: this.attachedComponent.ToJSON()
        }
    }
}
export class ItemRegistry extends registries.registry<Item<any>, ItemJSON>{
    constructor(space:string){
        super("item", space);
    }
}
export let bedrock;
export let air;
export let f = new core.RegistryFunc(async ()=>{
    let itemRegistry: ItemRegistry = new ItemRegistry("_");
    bedrock = itemRegistry.add(new Item("bedrock", Item.BlockItem ,new ItemProerties()));  
    air = itemRegistry.add(new Item("_", Item.BlockItem, new ItemProerties()));
});